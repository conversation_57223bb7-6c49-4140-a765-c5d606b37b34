// src/app/auth/callback/page.tsx
// Página de callback para autenticación de Supabase

'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/lib/supabase/supabaseClient';

function AuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Procesando autenticación...');

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const supabase = createClient();

        // Obtener código de autenticación de la URL
        const code = searchParams.get('code');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        console.log('🔄 [AUTH_CALLBACK] Processing callback:', { code: !!code, error, errorDescription });

        if (error) {
          console.error('❌ [AUTH_CALLBACK] Auth error:', error, errorDescription);
          setStatus('error');
          setMessage(errorDescription || 'Error en la autenticación');
          return;
        }

        if (code) {
          // Intercambiar código por sesión
          const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);

          if (exchangeError) {
            console.error('❌ [AUTH_CALLBACK] Error exchanging code:', exchangeError);
            setStatus('error');
            setMessage('Error procesando la autenticación');
            return;
          }

          if (data.user) {
            console.log('✅ [AUTH_CALLBACK] User authenticated:', data.user.id);

            // Verificar si es un usuario nuevo que necesita configurar contraseña
            const userMetadata = data.user.user_metadata || {};
            const needsPasswordSetup = !data.user.email_confirmed_at &&
                                     (userMetadata.created_via === 'free_registration' ||
                                      userMetadata.plan);

            if (needsPasswordSetup) {
              console.log('🔑 [AUTH_CALLBACK] User needs password setup, redirecting to reset-password');
              setStatus('success');
              setMessage('Configurando tu cuenta... Redirigiendo para establecer contraseña.');

              setTimeout(() => {
                router.push('/auth/reset-password');
              }, 1500);
              return;
            }

            setStatus('success');
            setMessage('¡Autenticación exitosa! Redirigiendo...');

            // Obtener información del plan del usuario
            const { data: profile } = await supabase
              .from('user_profiles')
              .select('subscription_plan, payment_verified, plan_expires_at')
              .eq('user_id', data.user.id)
              .single();

            console.log('📊 [AUTH_CALLBACK] User profile:', profile);

            // Redirigir según el estado del usuario
            setTimeout(() => {
              if (profile?.payment_verified) {
                // Usuario con pago verificado -> ir a la aplicación
                const transactionId = searchParams.get('transaction');
                const planId = profile.subscription_plan;

                if (planId === 'free') {
                  // Cuenta gratuita verificada -> ir directamente a la app
                  router.push('/app');
                } else {
                  // Cuenta de pago -> ir a welcome con información del plan
                  router.push(`/welcome?plan=${planId}&transaction=${transactionId}`);
                }
              } else {
                // Usuario sin pago verificado -> ir a pago
                router.push('/upgrade-plan');
              }
            }, 2000);
          }
        } else {
          // No hay código, verificar si ya está autenticado o si hay tokens en el hash
          console.log('🔍 [AUTH_CALLBACK] No code found, checking existing session...');

          const { data: { user } } = await supabase.auth.getUser();

          if (user) {
            console.log('✅ [AUTH_CALLBACK] User already authenticated:', user.id);
            setStatus('success');
            setMessage('Ya estás autenticado. Redirigiendo...');

            setTimeout(() => {
              router.push('/app');
            }, 1000);
          } else {
            console.log('❌ [AUTH_CALLBACK] No authentication found');
            setStatus('error');
            setMessage('No se encontró información de autenticación');
          }
        }

      } catch (error) {
        console.error('❌ [AUTH_CALLBACK] Callback error:', error);
        setStatus('error');
        setMessage('Error procesando la autenticación');
      }
    };

    handleAuthCallback();
  }, [router, searchParams]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
        {status === 'loading' && (
          <>
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Procesando...</h2>
            <p className="text-gray-600">{message}</p>
          </>
        )}
        
        {status === 'success' && (
          <>
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">¡Éxito!</h2>
            <p className="text-gray-600">{message}</p>
          </>
        )}
        
        {status === 'error' && (
          <>
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-800 mb-2">Error</h2>
            <p className="text-gray-600 mb-4">{message}</p>
            <div className="space-y-2">
              <button
                onClick={() => router.push('/login')}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
              >
                Ir a Login
              </button>
              <button
                onClick={() => router.push('/upgrade-plan')}
                className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
              >
                Ir a Planes
              </button>
            </div>
          </>
        )}
        
        <div className="mt-6 pt-4 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            ¿Problemas? <a href="/contact" className="text-blue-600 hover:underline">Contacta soporte</a>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function AuthCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Cargando...</h2>
          <p className="text-gray-600">Procesando autenticación...</p>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  );
}
